# Generated by Django 5.2.4 on 2025-07-14 08:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('books', '0005_author_alter_book_author'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Profile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gender', models.CharField(choices=[('M', '男'), ('F', '女'), ('S', '保密')], default='S', max_length=1, verbose_name='性别')),
                ('bio', models.TextField(blank=True, max_length=200, verbose_name='个性签名')),
            ],
        ),
        migrations.AlterModelOptions(
            name='author',
            options={'verbose_name': '作者', 'verbose_name_plural': '作者'},
        ),
        migrations.AlterModelOptions(
            name='book',
            options={'verbose_name': '书籍', 'verbose_name_plural': '书籍'},
        ),
        migrations.AlterModelOptions(
            name='chapter',
            options={'verbose_name': '章节', 'verbose_name_plural': '章节'},
        ),
        migrations.RemoveIndex(
            model_name='chapter',
            name='books_chapt_book_id_e95978_idx',
        ),
        migrations.RemoveIndex(
            model_name='chapter',
            name='books_chapt_updated_d6c489_idx',
        ),
        migrations.AddField(
            model_name='profile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
