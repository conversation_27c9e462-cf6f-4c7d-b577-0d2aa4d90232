from django.shortcuts import render, get_object_or_404,redirect,reverse
from .models import Book,Chapter,Comment,ReadingHistory,Profile # 导入模型
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db.models import Q,Count,F
from .forms import CustomUserCreationForm, UserUpdateForm,ProfileUpdateForm,CommentForm # 导入新表单
from django.contrib.auth import get_user_model # 【新增】导入此函数来获取项目的用户模型
from allauth.socialaccount.models import SocialAccount # 【新增】导入 SocialAccount 模型
# 小说列表视图
def book_list(request):
    # 预加载 author 外键，减少数据库查询
    all_books = Book.objects.select_related('author').all().order_by('-created_at')

    # 分页逻辑
    paginator = Paginator(all_books, 8)
    page = request.GET.get('page')

    try:
        page_obj = paginator.page(page)
    except PageNotAnInteger:
        page_obj = paginator.page(1)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)

    context = {
        'page_obj': page_obj,
    }

    return render(request, 'books/book_list.html', context)


@login_required
def book_detail(request, book_id):
    book = get_object_or_404(Book, id=book_id)
    # 【新增】当详情页被访问时，浏览量 +1
    # F() 表达式可以让我们在不读取对象到内存的情况下，直接在数据库层面高效地更新字段
    book.view_count = F('view_count') + 1
    book.save(update_fields=['view_count'])  # 只更新 view_count 字段，效率更高
    book.refresh_from_db()  # 从数据库重新加载数据，以获取最新的 view_count 值
    comments = book.comments.all()

    # --- 【新增这里】获取阅读历史 ---
    reading_history = None
    if request.user.is_authenticated:
        # 尝试获取当前用户对这本书的阅读历史记录
        reading_history = ReadingHistory.objects.filter(user=request.user, book=book).first()
    # --- 新增逻辑结束 ---

    # ... (处理评论表单的逻辑保持不变) ...
    if request.method == 'POST':
        comment_form = CommentForm(request.POST)
        if comment_form.is_valid():
            new_comment = comment_form.save(commit=False)
            new_comment.book = book
            new_comment.user = request.user
            new_comment.save()
            messages.success(request, '您的评论已成功发布！')
            return redirect('book_detail', book_id=book.id)
    else:
        comment_form = CommentForm()

    context = {
        'book': book,
        'comments': comments,
        'comment_form': comment_form,
        'reading_history': reading_history, # 【新增】把阅读历史传给模板
    }
    return render(request, 'books/book_detail.html', context)


# 添加此新视图函数以读取章节
def chapter_detail(request, book_id, chapter_id):
    chapter = get_object_or_404(Chapter, id=chapter_id, book__id=book_id)

    # --- 新增的逻辑从这里开始 ---

    # 获取本书的所有章节，并按顺序排序
    all_chapters = Chapter.objects.filter(book=chapter.book).order_by('order')

    # 查找上一章：在所有章节中，找到排序号比当前章节小的最后一章
    previous_chapter = all_chapters.filter(order__lt=chapter.order).last()

    # 查找下一章：在所有章节中，找到排序号比当前章节大的第一章
    next_chapter = all_chapters.filter(order__gt=chapter.order).first()

    # --- 新增的逻辑到这里结束 ---

    context = {
        'chapter': chapter,
        'previous_chapter': previous_chapter, # 把上一章对象传给模板
        'next_chapter': next_chapter,       # 把下一章对象传给模板
    }

    return render(request, 'books/chapter_detail.html', context)



# 新增用户注册功能
def signup(request):
    # 检查表单是否与数据一起提交（POST 请求）
    if request.method == 'POST':
        # 创建一个表单实例并使用请求中的数据填充它
        form = CustomUserCreationForm(request.POST)

        # 检查表单的数据是否有效（例如，用户名未被占用，密码匹配）
        if form.is_valid():
            # 如果表单有效，则将新用户保存到数据库
            form.save()

            # 从清理后的数据中获取用户名
            username = form.cleaned_data.get('username')

            # 创建成功消息，在下一页向用户展示
            messages.success(request, f'Account created for {username}! You can now log in.')

            # 将用户重定向到登录页面
            return redirect('login')

    # 如果不是 POST 请求，则必须是 GET 请求（用户只是访问页面）
    else:
        # 创建表单的空白实例
        form = CustomUserCreationForm()

    # 使用表单渲染注册页面模板
    return render(request, 'registration/signup.html', {'form': form})

@login_required
def profile(request):
    # 因为有 @login_required 的保护，我们在这里可以确信 request.user 就是当前登录的用户
    # 我们甚至不需要把 user 对象传递给模板，Django 会自动处理
    return render(request, 'registration/profile.html')

@login_required
def edit_profile(request):
    # 1. 使用 get_or_create 安全地获取或创建用户的 Profile，彻底解决崩溃问题
    profile, created = Profile.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        user_form = UserUpdateForm(request.POST, instance=request.user)
        profile_form = ProfileUpdateForm(request.POST, instance=profile)

        if user_form.is_valid() and profile_form.is_valid():
            user_form.save()
            profile_form.save()
            messages.success(request, '您的个人资料已成功更新！')
            return redirect('profile')
    else:
        user_form = UserUpdateForm(instance=request.user)
        profile_form = ProfileUpdateForm(instance=profile)

    # 2. 我们不再需要在视图里手动查找社交账号，所以 context 非常干净
    context = {
        'user_form': user_form,
        'profile_form': profile_form
    }
    return render(request, 'registration/edit_profile.html', context)


def search_results(request):
    # 从 URL 的 GET 参数中获取名为 'q' 的值，即用户输入的搜索词
    query = request.GET.get('q')
    results = [] # 先初始化一个空列表

    if query:
        # 如果用户确实输入了内容，则执行查询
        # Q 对象允许我们构建 OR 查询
        # __icontains 表示“包含此字符串，且不区分大小写”
        results = Book.objects.filter(
            Q(title__icontains=query) |
            Q(author__name__icontains=query) | # author__name 表示跨外键关系查询作者的名字
            Q(description__icontains=query)
        ).distinct() # distinct() 确保不会因为多重匹配而返回重复的书籍

    context = {
        'query': query,
        'results': results,
    }
    return render(request, 'books/search_results.html', context)

@login_required
def toggle_favorite(request, book_id):
    # 找到要操作的书籍
    book = get_object_or_404(Book, id=book_id)

    # 检查当前用户是否已经收藏了这本书
    if book in request.user.favorite_books.all():
        # 如果已收藏，则从收藏列表中移除
        request.user.favorite_books.remove(book)
        messages.success(request, f'您已将《{book.title}》移出收藏。')
    else:
        # 如果未收藏，则添加到收藏列表
        request.user.favorite_books.add(book)
        messages.success(request, f'成功收藏《{book.title}》！')

    # 操作完成后，重定向回书籍详情页
    return redirect('book_detail', book_id=book.id)

# 为“我的书架”创建一个专属视图
@login_required
def my_bookshelf(request):
    # 获取当前用户收藏的所有书籍
    favorite_books = request.user.favorite_books.all()
    context = {
        'favorite_books': favorite_books,
    }
    # 渲染一个新的模板文件
    return render(request, 'registration/my_bookshelf.html', context)





# 创建探索页面的视图
def explore_view(request):
    # 1. 获取所有筛选参数
    category_filter = request.GET.get('category', 'all') # 默认为 'all'
    status_filter = request.GET.get('status', 'all')     # 默认为 'all'
    sort_by = request.GET.get('sort', 'popular')         # 默认为 'popular'

    # 2. 从所有书籍开始筛选
    queryset = Book.objects.all()

    # 3. 应用分类和进度筛选
    if category_filter != 'all':
        queryset = queryset.filter(category=category_filter)

    if status_filter != 'all':
        queryset = queryset.filter(status=status_filter)

    # 4. 应用排序逻辑
    if sort_by == 'popular':
        # 按收藏数（热门）排序，收藏数相同则按创建时间排
        queryset = queryset.annotate(num_favorites=Count('favorites')).order_by('-num_favorites', '-created_at')
    elif sort_by == 'updated':
        # 按更新时间排序
        queryset = queryset.order_by('-updated_at')
    # 【注意】“字数”排序需要我们在模型中添加一个 word_count 字段并维护它，这是一个更高级的功能，我们暂时先不实现。
    else: # 默认排序
         queryset = queryset.order_by('-created_at')

    # 5. 应用分页
    paginator = Paginator(queryset, 12) # 每页显示12本书
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'category_choices': Book.CATEGORY_CHOICES,
        'status_choices': Book.STATUS_CHOICES,
        'current_category': category_filter,
        'current_status': status_filter,
        'current_sort': sort_by,
    }
    return render(request, 'books/explore.html', context)

def chapter_detail(request, book_id, chapter_id):
    chapter = get_object_or_404(Chapter, id=chapter_id, book__id=book_id)

    # --- 【新增这里】记录阅读历史的逻辑 ---
    # 首先，检查用户是否已登录
    if request.user.is_authenticated:
        # 使用 update_or_create 来查找或创建记录
        # 它会根据 user 和 book 这两个唯一的组合来查找
        ReadingHistory.objects.update_or_create(
            user=request.user,
            book=chapter.book,
            # defaults 字典包含了需要更新或在新创建时设置的字段
            defaults={'last_read_chapter': chapter}
        )
    # --- 新增逻辑结束 ---

    # ... (后面的“上一章/下一章”逻辑保持不变) ...
    all_chapters = Chapter.objects.filter(book=chapter.book).order_by('order')
    previous_chapter = all_chapters.filter(order__lt=chapter.order).last()
    next_chapter = all_chapters.filter(order__gt=chapter.order).first()

    context = {
        'chapter': chapter,
        'previous_chapter': previous_chapter,
        'next_chapter': next_chapter,
    }

    return render(request, 'books/chapter_detail.html', context)

# 【新增】创建排行榜页面的视图
def ranking_view(request):
    # 1. 热门推荐榜 (按收藏数) - Top 10
    hot_books = Book.objects.annotate(num_favs=Count('favorites')).order_by('-num_favs')[:10]

    # 2. 新书榜 (按创建时间) - Top 10
    new_books = Book.objects.order_by('-created_at')[:10]

    # 3. 热度榜 (按我们新增的浏览量) - Top 10
    popular_books = Book.objects.order_by('-view_count')[:10]

    # 4. 更新榜 (按更新时间) - Top 10
    updated_books = Book.objects.order_by('-updated_at')[:10]

    context = {
        'hot_books': hot_books,
        'new_books': new_books,
        'popular_books': popular_books,
        'updated_books': updated_books,
    }
    return render(request, 'books/ranking.html', context)

# 【新增】创建自定义的解绑视图
@login_required
def disconnect_social(request, account_id):
    # 确保请求是 POST，增加安全性
    if request.method == 'POST':
        # 找到要解绑的社交账号，并确保它属于当前登录的用户
        account = get_object_or_404(SocialAccount, id=account_id, user=request.user)
        account.delete()
        messages.success(request, f'成功解绑 {account.provider.capitalize()} 账号。')

    # 无论成功与否，都跳回编辑资料页面
    return redirect('edit_profile')