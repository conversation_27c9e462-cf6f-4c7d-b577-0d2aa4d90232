#changelist {
  display: block !important;
}

.change-list .filtered .results, .change-list .filtered .paginator, .filtered #toolbar, .filtered div.xfull {
  margin-right: 0px !important;
}

.submit-row {
  background: inherit !important;
  border: none !important;
}

#content {
  padding: 0;
}

#content-main{

  .page-header{
    margin-top: 0;
    margin-bottom: 20px;
    background-color: white;
    padding: 20px;
  }
  .related-widget-wrapper{
    display: inline-flex;
  }
  .module{
    margin:20px;
    margin-bottom: 0;
    padding: 10px;
    //顶部圆角
    border-radius: 10px 10px 0 0;
    background-color: white!important;
  }
  .submit-row{
    margin:20px;
    margin-top: 0;
    background-color: white!important;
    padding: 10px;
  }
}
.simpleui-form-item {
  display: inline-block;
  width: auto;
  margin-right: 10px;
}

#changelist #toolbar form input {
  padding-left: 30px !important;
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.el-link-btn {
  text-decoration: none !important;
  color: #606266 !important;
}

#pagination {
  text-align: center;
  margin-top: 10px;
}

.actions .el-button {
  margin-right: 10px;
  margin-left: 0px;
}

.change-list #content {
  padding: 0px;
  margin: 20px;
  background-color: white;
}

#content #changelist-form {
  padding: 0px 20px;
}

#changelist #toolbar {
  margin-bottom: 0px !important;
}

.form-main {
  //padding: 20px;
  float: none !important;
  width: auto !important;
}

.el-alert {
  margin-bottom: 10px;
}

.el-alert:last-child {
  margin-bottom: 0px !important;
}

a:link, a:visited {
  color: #3f9eff;
  text-decoration: none;
}

.object-tools {
  margin-top: 0;
  position: absolute;
  top: 13px;
  right: 30px;
}

.vTextField {
  outline: none;
}

td, th {
  vertical-align: inherit;
  border-bottom: none;
}

.row2 {
  background: #fafafa;
}

tbody tr:hover td, tbody tr:hover th {
  background-color: #ebf5ff;
}

.form-row {
  border-bottom: none !important;
  display:flex;
  align-items: center;
  // div:first-child{
  //   display: flex;
  //   align-items: center;
  //   gap: 10px;
  //   flex: 1;
  // }
  // div:not(:first-child){
  //   display: block;
  // }
  // div:not(.edui-default):not(.wmd-wrapper){
  //     display:flex;
  //     gap: 10px;
  //     align-items: center;
  // }
  .fileBox{
      flex:1;
  }
}
.submit-row{
  padding-bottom: 20px!important;
  display: flex;
  justify-content: space-between;

}
.submit-row input[type='submit'] {
  height: auto;
}

.submit-row input[type='submit'] {
  padding: 12px 20px !important;
}

@media (max-width: 767px) {
  .submit-row > * {
    margin-left: 0px !important;
    margin-bottom: 10px;
  }

  .form-main {
    padding: 0px;
  }

  #content {
    padding: 0px 3px !important;
  }

  .simpleui-form .el-input {
    width: 100px;
    padding-left: 0px !important;
    padding-right: 0px !important;
    margin: 0px;
    margin-right: 10px;
  }

  .object-tools {
    margin-top: 10px;
  }

  .el-pagination {
    white-space: inherit;
  }
}

@bg-color: #f1f4f6;

body,
#container {
  background-color: @bg-color !important;

  #content {

    #changelist-form {
      //background-color: #f3f3f4;
      //padding: 20px;
    }
  }
}
.aligned label{display: inline-block!important;}
@media (max-width: 767px) {
  .change-list #content {
    margin: 0px;
  }

  #content #changelist-form {
    padding: 0px 5px;
  }

  #content {
    box-shadow: none;
  }

  body, #container {
    background-color: white;
  }

  #changelist .actions {
    padding: 5px !important;
  }

  .el-button {
    padding: 8px 10px;
  }

  .object-tools {
    top: 6px !important;
    margin: 0px;
  }

  .el-pagination__total {
    display: block !important;
  }

  .el-pagination__jump {
    display: block !important;
    margin: 0px;
    margin-top: 10px;
  }

  .el-pager {
    margin: 0px;
    padding: 0px;
  }

  .el-dialog {
    width: 90%;
  }

}

@media (max-width: 1024px) {
  .change-list .filtered .results, .change-list .filtered .paginator, .filtered #toolbar, .filtered .actions, .filtered div.xfull {
    margin-right: 0px !important;
  }

  #changelist-search > div {
    max-width: 100% !important;
  }

  #content {
    padding: 0px !important;
  }
}

#changelist .actions {
  margin-right: auto;
  position: relative;

  .btn-group {
    position: absolute;
    right: 0px;
  }
}

.inline-group .tabular td.original p {
  margin-top: -42px !important;
}

:root {
  --error-fg: red!important;
}
#user_form{
  background-color: white;
  margin: 10px;
  padding: 10px;
  //color: #5a9cf8;
}
#id_password{
  .button{
    color: white!important;
  }
}
/*django新特性 主题适配*/
@media (prefers-color-scheme: dark) {
  :root {
    --primary: inherit;
    --primary-fg: inherit;

    --body-fg: inherit;
    --body-bg: inherit;
    --body-quiet-color: inherit;
    --body-loud-color: inherit;

    --breadcrumbs-link-fg: inherit;
    --breadcrumbs-bg: inherit;

    --link-fg: inherit;
    --link-hover-color: inherit;
    --link-selected-fg: #FFF;

    --hairline-color: inherit;
    --border-color: #ccc;

    --error-fg: inherit;
    --message-success-bg: inherit;
    --message-warning-bg: inherit;
    --message-error-bg: inherit;

    --darkened-bg: inherit;
    --selected-bg: inherit;
    --selected-row: inherit;

    --close-button-bg: inherit;
    --close-button-hover-bg: inherit;
  }
}