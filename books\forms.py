from .models import Comment # Comment 模型
from django import forms
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm
from .models import Profile #  Profile 模型

# 注册表单 (不变)
class CustomUserCreationForm(UserCreationForm):
    class Meta(UserCreationForm.Meta):
        model = User
        fields = ("username", "email")

# 更新 User 信息的表单 (只保留 email)
class UserUpdateForm(forms.ModelForm):
    email = forms.EmailField()
    class Meta:
        model = User
        fields = ['email']
        labels = {'email': '电子邮箱'}

# 更新 Profile 信息的表单
class ProfileUpdateForm(forms.ModelForm):
    class Meta:
        model = Profile
        fields = ['gender', 'bio']
        labels = {
            'gender': '性别',
            'bio': '个性签名',
        }
# 为发表评论创建一个表单
class CommentForm(forms.ModelForm):
    class Meta:
        model = Comment
        # 表单只包含 content 字段
        fields = ['content']
        # 自定义小工具，让输入框更好看
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': '在此输入你的评论...'
            }),
        }
        # 我们不希望显示 "Content:" 这样的标签，所以把它设置为空
        labels = {
            'content': '',
        }

