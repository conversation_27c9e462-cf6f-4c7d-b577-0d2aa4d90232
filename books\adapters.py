# books/adapters.py
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
# from allauth.account.adapter import DefaultAccountAdapter # <-- 新增导入
from allauth.account.utils import perform_login
from django.conf import settings
from django.urls import reverse
# from django.template.loader import select_template # <-- 新增导入，用于调试模板路径

class MySocialAccountAdapter(DefaultSocialAccountAdapter):
    def get_connect_redirect_url(self, request, socialaccount):
        """当一个社交账号“绑定”到已登录用户后，跳转到编辑资料页"""
        perform_login(
            request,
            request.user,
            email_verification=settings.ACCOUNT_EMAIL_VERIFICATION,
            redirect_url=None,
            signal_kwargs={"sociallogin": socialaccount},
        )
        return reverse('edit_profile')

    def get_signup_redirect_url(self, request, sociallogin):
        """当一个社交账号“注册”了一个新用户后，跳转到“设置密码”页面"""
        return reverse('account_set_password')

    def get_login_redirect_url(self, request, sociallogin):
        """
        当一个已存在的社交账号用户“登录”后，
        我们重写这个方法，让它总是跳转到网站首页。
        """
        return reverse('books:book_list') # 'books:book_list' 是我们首页的 URL 名称

# # 【新增这个类】
# class MyAccountAdapter(DefaultAccountAdapter):
#     def send_mail(self, template_prefix, email, context):
#         """
#         覆盖allauth的send_mail方法，用于调试。
#         template_prefix会是 'account/email/password_reset' 等。
#         """
#         print(f"\n--- DEBUG: Attempting to send mail to {email} ---")
#         print(f"Template prefix requested by allauth: {template_prefix}")
#
#         # 尝试找到并打印实际加载的模板文件路径
#         try:
#             # allauth通常会尝试加载两个文件：subject.txt 和 message.txt/.html
#             subject_template_names = [f"{template_prefix}_subject.txt"]
#             message_template_names = [f"{template_prefix}_message.html", f"{template_prefix}_message.txt"]
#
#             # 使用select_template来模拟Django的模板查找过程，并获取找到的模板对象
#             found_subject_template = select_template(subject_template_names)
#             found_message_template = select_template(message_template_names)
#
#             print(f"Subject template found at: {found_subject_template.origin.name if found_subject_template else 'NOT FOUND'}")
#             print(f"Message template found at: {found_message_template.origin.name if found_message_template else 'NOT FOUND'}")
#
#         except Exception as e:
#             print(f"Error during template lookup debugging: {e}")
#
#         print(f"Context for template: {context}")
#         print(f"--- DEBUG END ---\n")
#
#         # 确保邮件仍然被发送
#         super().send_mail(template_prefix, email, context)