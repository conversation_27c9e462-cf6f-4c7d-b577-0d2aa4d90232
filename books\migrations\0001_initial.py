# Generated by Django 5.2.4 on 2025-07-10 07:22

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Book',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='书名')),
                ('description', models.TextField(verbose_name='简介')),
                ('cover_image', models.ImageField(blank=True, default='covers/default_cover.png', null=True, upload_to='covers/%Y/%m/%d/', verbose_name='封面')),
                ('author', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='作者')),
            ],
        ),
        migrations.CreateModel(
            name='Chapter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=225, verbose_name='章节标题')),
                ('content', models.TextField(verbose_name='内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='发布时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='章节排序')),
                ('book', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chapters', to='books.book', verbose_name='所属小说')),
            ],
            options={
                'ordering': ['order', 'created_at'],
                'indexes': [models.Index(fields=['book', 'order', 'created_at'], name='books_chapt_book_id_e95978_idx'), models.Index(fields=['updated_at'], name='books_chapt_updated_d6c489_idx')],
            },
        ),
    ]
