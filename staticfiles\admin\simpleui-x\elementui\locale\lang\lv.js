'use strict';

exports.__esModule = true;
exports.default = {
  el: {
    colorpicker: {
      confirm: '<PERSON><PERSON>',
      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
    },
    datepicker: {
      now: '<PERSON>ad',
      today: '<PERSON><PERSON><PERSON>',
      cancel: 'Atcelt',
      clear: 'Notīr<PERSON>t',
      confirm: '<PERSON>i',
      selectDate: 'Izvēlēties datumu',
      selectTime: 'Izvēlēties laiku',
      startDate: 'Sākuma datums',
      startTime: 'Sākuma laiks',
      endDate: 'Beigu datums',
      endTime: 'Beigu laiks',
      prevYear: 'Iepriekšējais gads',
      nextYear: 'Nāka<PERSON>is gads',
      prevMonth: 'I<PERSON>riek<PERSON>ē<PERSON><PERSON> mēnesis',
      nextMonth: 'Nākama<PERSON> mēnesis',
      year: '',
      month1: 'Janvāris',
      month2: 'Febru<PERSON>ris',
      month3: 'Marts',
      month4: 'Aprīlis',
      month5: 'Maijs',
      month6: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      month7: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      month8: 'Augusts',
      month9: 'Septembris',
      month10: 'Oktobris',
      month11: 'Novembris',
      month12: 'Decembris',
      // week: 'nedēļa',
      weeks: {
        sun: 'Sv',
        mon: 'Pr',
        tue: 'Ot',
        wed: 'Tr',
        thu: 'Ce',
        fri: 'Pk',
        sat: 'Se'
      },
      months: {
        jan: 'Jan',
        feb: 'Feb',
        mar: 'Mar',
        apr: 'Apr',
        may: 'Mai',
        jun: 'Jūn',
        jul: 'Jūl',
        aug: 'Aug',
        sep: 'Sep',
        oct: 'Okt',
        nov: 'Nov',
        dec: 'Dec'
      }
    },
    select: {
      loading: 'Ielādē',
      noMatch: 'Nav atbilstošu datu',
      noData: 'Nav datu',
      placeholder: 'Izvēlēties'
    },
    cascader: {
      noMatch: 'Nav atbilstošu datu',
      loading: 'Ielādē',
      placeholder: 'Izvēlēties',
      noData: 'Nav datu'
    },
    pagination: {
      goto: 'Iet uz',
      pagesize: '/lapa',
      total: 'Kopā {total}',
      pageClassifier: ''
    },
    messagebox: {
      title: 'Paziņojums',
      confirm: 'Labi',
      cancel: 'Atcelt',
      error: 'Nederīga ievade'
    },
    upload: {
      deleteTip: 'Nospiediet dzēst lai izņemtu',
      delete: 'Dzēst',
      preview: 'Priekšskatīt',
      continue: 'Turpināt'
    },
    table: {
      emptyText: 'Nav datu',
      confirmFilter: 'Apstiprināt',
      resetFilter: 'Atiestatīt',
      clearFilter: 'Visi',
      sumText: 'Summa'
    },
    tree: {
      emptyText: 'Nav datu'
    },
    transfer: {
      noMatch: 'Nav atbilstošu datu',
      noData: 'Nav datu',
      titles: ['Saraksts 1', 'Saraksts 2'],
      filterPlaceholder: 'Ievadīt atslēgvārdu',
      noCheckedFormat: '{total} vienības',
      hasCheckedFormat: '{checked}/{total} atzīmēti'
    },
    image: {
      error: 'FAILED' // to be translated
    },
    pageHeader: {
      title: 'Back' // to be translated
    },
    popconfirm: {
      confirmButtonText: 'Yes', // to be translated
      cancelButtonText: 'No' // to be translated
    },
    empty: {
      description: 'Nav datu'
    }
  }
};