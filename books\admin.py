from django.contrib import admin
from .models import Book, Chapter,Author,ReadingHistory
from django.db import models # 导入 models 模块

# 这是一个内联模型配置，目的是在管理“书”的页面上直接添加或修改“章节”
# TabularInline 表示章节信息以表格形式显示
class ChapterInline(admin.TabularInline):
    model = Chapter
    extra = 1  # 默认提供 1 个额外的空章节表单，方便快速添加
    fields = ('title', 'order', 'content')  # 在内联中显示的字段
    ordering = ('order',)  # 按 order 字段排序
    # 设置 content 字段的显示方式，允许更好的编辑体验
    formfield_overrides = {
        models.TextField: {'widget': admin.widgets.AdminTextareaWidget(attrs={'rows': 2, 'cols': 60})}
    }

@admin.register(Author)
class AuthorAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)

# 注册 Book 模型，并自定义管理界面
@admin.register(Book)
class BookAdmin(admin.ModelAdmin):
    list_display = ('title', 'author', 'category', 'status', 'created_at')  # 显示书名、作者和创建时间
    list_filter = ('author', 'category', 'status', 'created_at') # 提供按作者和创建时间过滤的功能
    search_fields = ('title', 'author__name')  # 支持按书名和作者名搜索
    inlines = [ChapterInline]  # 将章节内联到书籍的管理页面
    # 添加这一行来预加载 author 对象
    list_select_related = ('author',)
    # 自定义显示作者的用户名,两个管理页面都将受到N+1查询性能问题的保护
    def author_username(self, obj):
        return obj.author.username if obj.author else "No Author"  # 如果没有作者，显示 "No Author"
    author_username.admin_order_field = 'author'  # 支持按作者排序
    author_username.short_description = 'Author'  # 自定义列标题

# 注册 Chapter 模型，并自定义管理界面
@admin.register(Chapter)
class ChapterAdmin(admin.ModelAdmin):
    list_display = ('title', 'book', 'order', 'created_at', 'updated_at')  # 显示章节标题、所属书籍、排序、创建时间和更新时间
    list_filter = ('book','created_at')  # 提供按书籍和创建时间过滤的功能
    search_fields = ('title', 'book__title')  # 支持按章节标题和书名搜索
    list_editable = ('order',)  # 允许直接编辑章节排序

    # 关键性能优化：预加载 book 对象
    list_select_related = ('book',)

# 注册 ReadingHistory 模型到后台
@admin.register(ReadingHistory)
class ReadingHistoryAdmin(admin.ModelAdmin):
    # 在列表页上显示的字段
    list_display = ('user', 'book', 'last_read_chapter', 'updated_at')

    # 添加过滤器，方便按用户或书籍筛选
    list_filter = ('user', 'book')

    # 在顶部添加一个按时间筛选的层级导航
    date_hierarchy = 'updated_at'
