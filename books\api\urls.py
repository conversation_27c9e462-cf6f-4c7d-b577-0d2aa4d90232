# books/api/urls.py

from django.urls import path
# Notice we are importing the corrected view names here
from .views import (
    BookListCreateAPIView,
    BookRetrieveUpdateDestroyAPIView,
    ChapterListView,
    ChapterDetailView
)

urlpatterns = [
    # Book APIs
    path('books/', BookListCreateAPIView.as_view(), name='api-book-list'),
    path('books/<int:pk>/', BookRetrieveUpdateDestroyAPIView.as_view(), name='api-book-detail'),

    # Chapter APIs
    path('books/<int:book_pk>/chapters/', ChapterListView.as_view(), name='api-chapter-list'),
    path('chapters/<int:pk>/', ChapterDetailView.as_view(), name='api-chapter-detail'),
]