from django.urls import path
from . import views  # 从当前文件夹导入 views.py
# app_name = 'books'
urlpatterns = [
    # 当访问根路径''时，调用views.py里的book_list函数
    # name='book_list' 是给这个URL起个名字，方便以后在模板中引用
    path('', views.book_list, name='book_list'),

    path('search/', views.search_results, name='search_results'),
    # 为新的探索页面创建 URL
    path('explore/', views.explore_view, name='explore'),
    #排行榜页面
    path('ranking/', views.ranking_view, name='ranking'),

    # 为切换收藏状态的功能创建 URL
    path('books/<int:book_id>/favorite/', views.toggle_favorite, name='toggle_favorite'),
    path('books/<int:book_id>/', views.book_detail, name='book_detail'),
    path('books/<int:book_id>/chapters/<int:chapter_id>/', views.chapter_detail, name='chapter_detail'),
]
