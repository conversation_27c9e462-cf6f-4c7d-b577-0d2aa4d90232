(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('element/locale/ms', ['module', 'exports'], factory);
  } else if (typeof exports !== "undefined") {
    factory(module, exports);
  } else {
    var mod = {
      exports: {}
    };
    factory(mod, mod.exports);
    global.ELEMENT.lang = global.ELEMENT.lang || {}; 
    global.ELEMENT.lang.ms = mod.exports;
  }
})(this, function (module, exports) {
  'use strict';

  exports.__esModule = true;
  exports.default = {
    el: {
      colorpicker: {
        confirm: 'Sah',
        clear: 'Padam'
      },
      datepicker: {
        now: 'Sekarang',
        today: 'Hari ini',
        cancel: 'Bat<PERSON>',
        clear: 'Padam',
        confirm: 'Sah',
        selectDate: 'Pilih <PERSON>h',
        selectTime: 'Pilih <PERSON>sa',
        startDate: 'Tarikh <PERSON>',
        startTime: 'Masa <PERSON>',
        endDate: '<PERSON>rikh <PERSON>',
        endTime: '<PERSON>sa <PERSON>',
        prevYear: '<PERSON><PERSON>',
        nextYear: '<PERSON><PERSON> Depan',
        prevMonth: '<PERSON><PERSON><PERSON>',
        nextMonth: '<PERSON>ulan <PERSON>',
        year: '<PERSON><PERSON>',
        month1: 'Januari',
        month2: 'Febuari',
        month3: 'Mac',
        month4: 'April',
        month5: 'Mei',
        month6: 'Jun',
        month7: 'Julai',
        month8: 'Ogos',
        month9: 'September',
        month10: 'Oktober',
        month11: 'November',
        month12: 'Disember',
        weeks: {
          sun: 'Ahad',
          mon: 'Isnin',
          tue: 'Selasa',
          wed: 'Rabu',
          thu: 'Khamis',
          fri: 'Jumaat',
          sat: 'Sabtu'
        },
        months: {
          jan: 'Januari',
          feb: 'Febuari',
          mar: 'Mac',
          apr: 'April',
          may: 'Mei',
          jun: 'Jun',
          jul: 'Julai',
          aug: 'Ogos',
          sep: 'September',
          oct: 'Oktober',
          nov: 'November',
          dec: 'Disember'
        }
      },
      select: {
        loading: 'Sedang dimuat turun',
        noMatch: 'Tiada maklumat yang sepadan',
        noData: 'Tiada maklumat',
        placeholder: 'Sila pilih'
      },
      cascader: {
        noMatch: 'Tiada maklumat yang sepadan',
        loading: 'Sedang dimuat turun',
        placeholder: 'Sila pilih',
        noData: 'Tiada maklumat'
      },
      pagination: {
        goto: 'Seterusnya',
        pagesize: 'x/Halaman',
        total: 'Jumlah {total} ',
        pageClassifier: 'Halaman'
      },
      messagebox: {
        title: 'Tip',
        confirm: 'Sah',
        cancel: 'Batal',
        error: 'Data yang diisi tidak sah!'
      },
      upload: {
        deleteTip: 'Tekan "Padam" untuk memadam',
        delete: 'Padam',
        preview: 'Pratonton gambar',
        continue: 'Meneruskan muat naik'
      },
      table: {
        emptyText: 'Tiada maklumat',
        confirmFilter: 'Tapis',
        resetFilter: 'Set Semula',
        clearFilter: 'Semua',
        sumText: 'Jumlah'
      },
      tree: {
        emptyText: 'Tiada maklumat'
      },
      transfer: {
        noMatch: 'Tiada maklumat yang sepadan',
        noData: 'Tiada maklumat',
        titles: ['Senarai 1', 'Senarai 2'],
        filterPlaceholder: 'Masukkan kandungan carian',
        noCheckedFormat: 'Jumlah {total} item',
        hasCheckedFormat: 'Telah memilih {checked}/{total} item'
      },
      image: {
        error: 'Muat turun gagal'
      },
      pageHeader: {
        title: 'Kembali'
      },
      popconfirm: {
        confirmButtonText: 'Sah',
        cancelButtonText: 'Batal'
      },
      empty: {
        description: 'Tiada maklumat'
      }
    }
  };
  module.exports = exports['default'];
});