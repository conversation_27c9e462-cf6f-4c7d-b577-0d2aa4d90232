(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('element/locale/eu', ['module', 'exports'], factory);
  } else if (typeof exports !== "undefined") {
    factory(module, exports);
  } else {
    var mod = {
      exports: {}
    };
    factory(mod, mod.exports);
    global.ELEMENT.lang = global.ELEMENT.lang || {}; 
    global.ELEMENT.lang.eu = mod.exports;
  }
})(this, function (module, exports) {
  'use strict';

  exports.__esModule = true;
  exports.default = {
    el: {
      colorpicker: {
        confirm: 'Ados',
        clear: 'Garbitu'
      },
      datepicker: {
        now: 'Orain',
        today: 'Gaur',
        cancel: 'Utzi',
        clear: 'Garbitu',
        confirm: 'Ados',
        selectDate: 'Hautatu data',
        selectTime: 'Hautatu ordua',
        startDate: 'Hasierako data',
        startTime: 'Hasierako ordua',
        endDate: 'Amaierako data',
        endTime: 'Amaierako ordua',
        prevYear: 'Aurreko urtea',
        nextYear: 'Hurrengo urtea',
        prevMonth: 'Aurreko hilabetea',
        nextMonth: 'Hurrengo hilabetea',
        year: '',
        month1: 'Urtarrila',
        month2: 'Otsaila',
        month3: 'Martxoa',
        month4: 'Apirila',
        month5: 'Maiatza',
        month6: 'Ekaina',
        month7: 'Uztaila',
        month8: 'Abuztua',
        month9: 'Iraila',
        month10: 'Urria',
        month11: 'Azaroa',
        month12: 'Abendua',
        // week: 'astea',
        weeks: {
          sun: 'ig.',
          mon: 'al.',
          tue: 'ar.',
          wed: 'az.',
          thu: 'og.',
          fri: 'ol.',
          sat: 'lr.'
        },
        months: {
          jan: 'urt',
          feb: 'ots',
          mar: 'mar',
          apr: 'api',
          may: 'mai',
          jun: 'eka',
          jul: 'uzt',
          aug: 'abu',
          sep: 'ira',
          oct: 'urr',
          nov: 'aza',
          dec: 'abe'
        }
      },
      select: {
        loading: 'Kargatzen',
        noMatch: 'Bat datorren daturik ez',
        noData: 'Daturik ez',
        placeholder: 'Hautatu'
      },
      cascader: {
        noMatch: 'Bat datorren daturik ez',
        loading: 'Kargatzen',
        placeholder: 'Hautatu',
        noData: 'Daturik ez'
      },
      pagination: {
        goto: 'Joan',
        pagesize: '/orria',
        total: 'Guztira {total}',
        pageClassifier: ''
      },
      messagebox: {
        title: 'Mezua',
        confirm: 'Ados',
        cancel: 'Utzi',
        error: 'Sarrera baliogabea'
      },
      upload: {
        deleteTip: 'sakatu Ezabatu kentzeko',
        delete: 'Ezabatu',
        preview: 'Aurrebista',
        continue: 'Jarraitu'
      },
      table: {
        emptyText: 'Daturik ez',
        confirmFilter: 'Baieztatu',
        resetFilter: 'Berrezarri',
        clearFilter: 'Guztia',
        sumText: 'Batura'
      },
      tree: {
        emptyText: 'Daturik ez'
      },
      transfer: {
        noMatch: 'Bat datorren daturik ez',
        noData: 'Daturik ez',
        titles: ['Zerrenda 1', 'Zerrenda 2'], // to be translated
        filterPlaceholder: 'Sartu gako-hitza', // to be translated
        noCheckedFormat: '{total} elementu', // to be translated
        hasCheckedFormat: '{checked}/{total} hautatuta' // to be translated
      },
      image: {
        error: 'FAILED' // to be translated
      },
      pageHeader: {
        title: 'Back' // to be translated
      },
      popconfirm: {
        confirmButtonText: 'Yes', // to be translated
        cancelButtonText: 'No' // to be translated
      },
      empty: {
        description: 'Daturik ez'
      }
    }
  };
  module.exports = exports['default'];
});