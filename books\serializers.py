from rest_framework import serializers
from .models import Book, Author,Chapter

# 我们可以为 Author 创建一个简单的序列化器，这样在 Book API 中作者信息会更清晰
class AuthorSerializer(serializers.ModelSerializer):
    class Meta:
        model = Author
        fields = ['id', 'name']

class BookSerializer(serializers.ModelSerializer):
    # 使用我们上面定义的 AuthorSerializer 来更好地展示作者信息
    # read_only=True 表示这个字段只用于读取，不能通过 API 修改
    author = AuthorSerializer(read_only=True)

    class Meta:
        model = Book
        # 这里我们使用模型中真实存在的字段
        fields = [
            'id', 
            'title', 
            'author', 
            'description', 
            'category', 
            'status', 
            'cover_image',
            'view_count'
        ]

# 用于章节列表，不包含巨大的 content 字段，提高加载速度
class ChapterListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Chapter
        fields = ['id', 'title', 'order']

# 用于章节详情，包含所有信息
class ChapterDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = Chapter
        fields = ['id', 'title', 'content', 'order', 'book']