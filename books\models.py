from django.core.exceptions import ValidationError
from django.db import models  # 导入 Django 的模型类，继承所有模型功能
from django.contrib.auth.models import User  # 导入 Django 内置的用户系统模型
from django.db import models, transaction  # 导入事务管理器
from PIL import Image


# 定义一个作者模型
class Author(models.Model):
    name = models.CharField(max_length=100, unique=True, verbose_name="作者姓名")
    bio = models.TextField(blank=True, null=True, verbose_name="作者简介")
    class Meta:
        verbose_name = '作者'
        verbose_name_plural = '作者'

    def __str__(self):
        return self.name
# 定义一个书籍模型
class Book(models.Model):

    CATEGORY_CHOICES = [
        ('FANTASY', '玄幻奇幻'),
        ('WUXIA', '武侠仙侠'),
        ('URBAN', '都市生活'),
        ('HISTORY', '历史军事'),
        ('SCIFI', '科幻末世'),
        ('GAMING', '游戏竞技'),
        ('OTHER', '其他分类'),
    ]
    STATUS_CHOICES = [
        ('ONGOING', '连载中'),
        ('COMPLETED', '已完结'),
    ]
    # 书名字段，最大长度 100
    title = models.CharField(max_length=100, verbose_name='书名')

    # 外键关系，指向 Django 内置的 User 模型，表示书籍的作者
    author = models.ForeignKey(
        Author,
        on_delete=models.SET_NULL, # 如果作者被删除，书籍的作者字段设为 NULL
        null=True,
        blank=True,
        verbose_name="作者"
    )

    # 小说简介，TextField 适合存储较长的文本内容
    description = models.TextField(verbose_name='简介')

    # 小说封面图片，upload_to 指定了文件上传后存储在 media/covers/ 目录下
    # 文件将按年月日进行分类存储（例如：media/covers/2025/07/08/xxx.jpg）
    cover_image = models.ImageField(
        upload_to='covers/%Y/%m/%d/',  # 按年月日分目录存储
        blank=True,  # 允许为空
        null=True,  # 允许为 null
        default='covers/default_cover.png',  # 默认封面图路径
        verbose_name='封面'
    )

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')


    # 封面图片大小验证：最大文件大小为 2MB
    def clean(self):
        super().clean()  # 调用父类的 clean 方法，确保其他验证能生效

        # 检查是否上传了新文件
        if self.cover_image and hasattr(self.cover_image, 'file'):
            # 1. 检查文件大小（限制为 2MB）
            if self.cover_image.size > 2 * 1024 * 1024:
                raise ValidationError('封面上传大小不能超过 2MB!')

            # 2. 可选：如果不需要限制尺寸，可以移除比例检查
            # 检查图片是否能被打开（不限制格式）
            try:
                image = Image.open(self.cover_image)
                # 此处可以添加其他对图片的检查，但我们现在不进行宽高比和格式限制

            except Exception as e:
                # 如果上传的文件不是有效的图片格式，会抛出异常
                raise ValidationError(f'无法识别的图片文件格式: {e}')

    # __str__ 方法用来返回该模型的字符串表示，显示书名
    def __str__(self):
        return self.title  # 显示书籍标题



    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='OTHER', verbose_name='分类')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='ONGOING', verbose_name='状态')

    class Meta:
        verbose_name = '书籍'
        verbose_name_plural = '书籍'

        # 【新增这里】创建与 User 模型的多对多关系
        # blank=True 表示一本书可以没有任何人收藏
        # related_name='favorite_books' 让我们能通过 user.favorite_books.all() 找到一个用户收藏的所有书籍

    favorites = models.ManyToManyField(User, related_name='favorite_books', blank=True, verbose_name="收藏者")
    view_count = models.PositiveIntegerField(default=0, verbose_name="浏览量")


# 章节模型定义
class Chapter(models.Model):
    # 外键关系，关联到 Book 模型，表示该章节所属的书籍
    # related_name='chapters' 允许我们通过 book.chapters 来访问该书籍的所有章节
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='chapters', verbose_name="所属小说")

    # 章节标题字段，最大长度 225
    title = models.CharField(max_length=225, verbose_name='章节标题')

    # 章节内容，存储该章节的具体内容，TextField 适合较长文本
    content = models.TextField(verbose_name='内容')

    # 创建时间字段，自动设置为章节创建时的时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='发布时间')

    # 更新时间字段，每次更新时自动更新为当前时间
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    # 章节排序字段，用于控制章节的顺序
    # PositiveIntegerField 用于存储正整数，保证章节顺序从 1 开始
    order = models.PositiveIntegerField(default=0, verbose_name='章节排序')

    # Meta 类用于定义模型的元数据
    class Meta:
        # ordering 定义了章节的默认排序方式：按 order 字段升序，再按创建时间升序
        ordering = ['order', 'created_at']

        # 索引优化：为 book, order 和 created_at 字段添加数据库索引以提升查询性能
        indexes = [
            models.Index(fields=['book', 'order', 'created_at']),  # 联合索引（多个字段联合索引）
            models.Index(fields=['updated_at']),  # 单独索引，用于优化更新时间字段的查询
        ]

    # 重写 save 方法，确保章节按照正确的顺序排序
    def save(self, *args, **kwargs):
        if not self.id:  # 新建章节时，自动根据当前书籍的已存在章节来计算章节顺序
            with transaction.atomic():  # 使用事务保证并发安全
                last_order = Chapter.objects.filter(book=self.book).aggregate(
                    models.Max('order')  # 获取当前书籍的最后一个章节的 order
                )['order__max'] or 0  # 如果没有章节，则 last_order 默认为 0
                self.order = last_order + 1  # 为新章节设置顺序号
        super().save(*args, **kwargs)  # 调用父类的 save 方法进行保存

    # __str__ 方法用于返回章节的字符串表示，在 Django 管理后台中显示章节信息
    def __str__(self):
        return f'{self.book.title if self.book else "暂无小说"} - {self.title}'
        # 如果没有关联书籍，显示 '暂无小说'，否则显示书籍标题和章节标题

    class Meta:
        verbose_name = '章节'
        verbose_name_plural = '章节'

#个人资料
class Profile(models.Model):
    # 建立与 User 模型的一对一关联，这是核心
    # on_delete=models.CASCADE 表示当 User 被删除时，其关联的 Profile 也一并删除
    user = models.OneToOneField(User, on_delete=models.CASCADE)

    # 性别字段，使用 choices 提供固定选项
    GENDER_CHOICES = [
        ('M', '男'),
        ('F', '女'),
        ('S', '保密'),
    ]
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, default='S', verbose_name='性别')

    # 个性签名字段，允许为空
    bio = models.TextField(max_length=200, blank=True, verbose_name='个性签名')

    def __str__(self):
        return f'{self.user.username} 的个人资料'

class Comment(models.Model):
    # 评论所属的书籍。一本书可以有多条评论。
    book = models.ForeignKey(Book, on_delete=models.CASCADE, related_name='comments', verbose_name='评论书籍')
    # 发表评论的用户。一个用户可以有多条评论。
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='comments', verbose_name='评论用户')
    # 评论内容
    content = models.TextField(verbose_name='评论内容')
    # 评论创建时间，自动记录
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='评论时间')

    class Meta:
        # 设置默认排序，让最新的评论显示在最前面
        ordering = ['-created_at']

    def __str__(self):
        return f'用户 {self.user.username} 对《{self.book.title}》的评论'


class ReadingHistory(models.Model):
    # 关联到用户。一个用户可以有多条阅读历史。
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")

    # 关联到书籍。
    book = models.ForeignKey(Book, on_delete=models.CASCADE, verbose_name="书籍")

    # 记录用户在这本书上最后阅读到的章节。
    # on_delete=models.SET_NULL 表示如果这个章节被删除了，历史记录里的这个字段会变为空，但历史记录本身不会被删除。
    last_read_chapter = models.ForeignKey(Chapter, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="最后阅读章节")

    # 记录最后一次阅读的时间，每次更新记录时都会自动更新这个时间。
    updated_at = models.DateTimeField(auto_now=True, verbose_name="最后阅读时间")

    class Meta:
        # 确保一个用户对一本书只有一条阅读历史记录，这叫“联合唯一约束”。
        unique_together = ('user', 'book')
        ordering = ['-updated_at'] # 默认按更新时间倒序

    def __str__(self):
        return f"{self.user.username} 对《{self.book.title}》的阅读历史"
