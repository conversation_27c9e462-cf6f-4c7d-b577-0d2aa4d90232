(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('element/locale/km', ['module', 'exports'], factory);
  } else if (typeof exports !== "undefined") {
    factory(module, exports);
  } else {
    var mod = {
      exports: {}
    };
    factory(mod, mod.exports);
    global.ELEMENT.lang = global.ELEMENT.lang || {}; 
    global.ELEMENT.lang.km = mod.exports;
  }
})(this, function (module, exports) {
  'use strict';

  exports.__esModule = true;
  exports.default = {
    el: {
      colorpicker: {
        confirm: 'យល់ព្រម',
        clear: 'លុប'
      },
      datepicker: {
        now: 'ឥឡូវនេះ',
        today: 'ថ្ងៃនេះ',
        cancel: 'បោះបង់',
        clear: 'លុប',
        confirm: 'យល់ព្រម',
        selectDate: 'ជ្រើសរើសថ្ងៃ',
        selectTime: 'ជ្រើសរើសម៉ោង',
        startDate: 'ថ្ងៃចាប់ផ្តើម',
        startTime: 'ម៉ោងចាប់ផ្តើម',
        endDate: 'ថ្ងៃបញ្ចប់',
        endTime: 'ម៉ោងបញ្ចប់',
        prevYear: 'ឆ្នាំមុន',
        nextYear: 'ឆ្នាំក្រោយ',
        prevMonth: 'ខែមុន',
        nextMonth: 'ខែក្រោយ',
        year: 'ឆ្នាំ',
        month1: 'មករា',
        month2: 'កុម្ភៈ',
        month3: 'មីនា',
        month4: 'មេសា',
        month5: 'ឧសភា',
        month6: 'មិថុនា',
        month7: 'កក្កដា',
        month8: 'សីហា',
        month9: 'កញ្ញា',
        month10: 'តុលា',
        month11: 'វិច្ឆកា',
        month12: 'ធ្នូ',
        week: 'សប្តាហ៍',
        weeks: {
          sun: 'អាទិត្យ',
          mon: 'ច័ន្ទ',
          tue: 'អង្គារ',
          wed: 'ពុធ',
          thu: 'ព្រ.ហ',
          fri: 'សុក្រ',
          sat: 'សៅរ៏'
        },
        months: {
          jan: 'មករា',
          feb: 'កុម្ភៈ',
          mar: 'មីនា',
          apr: 'មេសា',
          may: 'ឧសភា',
          jun: 'មិថុនា',
          jul: 'កក្កដា',
          aug: 'សីហា',
          sep: 'កញ្ញា',
          oct: 'តុលា',
          nov: 'វិច្ឆកា',
          dec: 'ធ្នូ'
        }
      },
      select: {
        loading: 'កំពុងផ្ទុក',
        noMatch: 'គ្មានទិន្ន័យដូច',
        noData: 'គ្មានទិន្ន័យ',
        placeholder: 'ជ្រើសរើស'
      },
      cascader: {
        noMatch: 'គ្មានទិន្ន័យដូច',
        loading: 'កំពុងផ្ទុក',
        placeholder: 'ជ្រើសរើស',
        noData: 'គ្មានទិន្ន័យ'
      },
      pagination: {
        goto: 'ទៅកាន់',
        pagesize: '/ទំព័រ',
        total: 'សរុប {total}',
        pageClassifier: ''
      },
      messagebox: {
        title: 'សារ',
        confirm: 'យល់ព្រម',
        cancel: 'បោះបង់',
        error: 'ការបញ្ចូលមិនអនុញ្ញាត'
      },
      upload: {
        deleteTip: 'ចុចលុបដើម្បីដកចេញ',
        delete: 'លុប',
        preview: 'មើល',
        continue: 'បន្ត'
      },
      table: {
        emptyText: 'គ្មានទិន្ន័យ',
        confirmFilter: 'យល់ព្រម',
        resetFilter: 'កំណត់ឡើងវិញ',
        clearFilter: 'ទាំងអស់',
        sumText: 'បូក'
      },
      tree: {
        emptyText: 'គ្មានទិន្ន័យ'
      },
      transfer: {
        noMatch: 'គ្មានទិន្ន័យដូច',
        noData: 'គ្មានទិន្ន័យ',
        titles: ['បញ្ជី ១', 'បញ្ជី ២'],
        filterPlaceholder: 'បញ្ចូលពាក្យ',
        noCheckedFormat: '{total} ធាតុ',
        hasCheckedFormat: '{checked}/{total} បានគូសធីក'
      },
      image: {
        error: 'បរាជ័យ'
      },
      pageHeader: {
        title: 'Back'
      },
      popconfirm: {
        confirmButtonText: 'ព្រម',
        cancelButtonText: 'មិនព្រម'
      },
      empty: {
        description: 'គ្មានទិន្ន័យ'
      }
    }
  };
  module.exports = exports['default'];
});