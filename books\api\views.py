# books/api/views.py

from rest_framework import generics
from ..models import Book, Chapter
from ..serializers import BookSerializer, ChapterListSerializer, ChapterDetailSerializer

# View for listing all books or creating a new book
class BookListCreateAPIView(generics.ListCreateAPIView):
    queryset = Book.objects.all().order_by('-created_at')
    serializer_class = BookSerializer

# View for retrieving, updating, or deleting a single book
class BookRetrieveUpdateDestroyAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Book.objects.all()
    serializer_class = BookSerializer

# View for listing all chapters for a specific book
class ChapterListView(generics.ListAPIView):
    serializer_class = ChapterListSerializer

    def get_queryset(self):
        book_id = self.kwargs['book_pk']
        return Chapter.objects.filter(book_id=book_id).order_by('order')

# View for retrieving a single chapter's details
class ChapterDetailView(generics.RetrieveAPIView):
    queryset = Chapter.objects.all()
    serializer_class = ChapterDetailSerializer