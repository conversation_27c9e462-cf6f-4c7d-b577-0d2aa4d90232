"""
URL configuration for Novel_website project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path,include
from django.conf import settings
from django.conf.urls.static import static
# 导入我们 books 应用的视图，并给它一个别名 book_views
from books import views as book_views
# 【新增这里】导入 Django 内置的认证视图，并给它起个别名 auth_views
from django.contrib.auth import views as auth_views

# --- 导入 DRF 的 obtain_auth_token 视图 ---
from rest_framework.authtoken import views as authtoken_views

urlpatterns = [
    path('admin/', admin.site.urls),

   # 这一行现在处理所有与账户相关的页面。
    path('accounts/', include('allauth.urls')),

    # 【新增】包含 allauth 的所有 URL，包括QQ登录需要的回调地址
    path('accounts/', include('allauth.urls')),

    path('', include('books.urls')), # 将根URL指向books应用的urls
    # path('signup/', book_views.signup, name='signup'),

# 我们直接使用 Django 的 LoginView，并告诉它使用我们刚创建的模板
#     path('login/', auth_views.LoginView.as_view(template_name='registration/login.html'), name='login'),

    # 个人中心创建 URL
    path('profile/', book_views.profile, name='profile'),

    # LogoutView 会自动处理用户的退出操作，我们甚至不需要为它创建模板
    # path('logout/', auth_views.LogoutView.as_view(next_page='/'), name='logout'),
    # 'next_page='/'` 表示登出后会重定向到网站的根目录（首页）

    # 为编辑资料页面创建 URL
    path('profile/edit/', book_views.edit_profile, name='edit_profile'),

# 【新增】为我们的自定义解绑功能创建 URL
    path('accounts/disconnect/<int:account_id>/', book_views.disconnect_social, name='disconnect_social'),

    path('password/change/',
         auth_views.PasswordChangeView.as_view(
            # template_name='registration/password_change.html', # 指定使用的模板
            success_url='/profile/' # 修改成功后跳转的地址
         ),
         name='password_change'),

    path('bookshelf/', book_views.my_bookshelf, name='my_bookshelf'),

    # 为我们的API提供一个统一的入口，比如 'api/'
    path('api/', include('books.api.urls')),
   # 为获取认证令牌提供一个 API 端点
    path('api/api-token-auth/', authtoken_views.obtain_auth_token, name='api_token_auth'),
]


if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
