"""
Django settings for Novel_website project.

Generated by 'django-admin startproject' using Django 5.2.4.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""
import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-sfzi=q1po)3mkl_hzy40pjz*f4ibd_cgx)o=p%4a+%3w9=vk0h'

# 安全警告：不要在生产中打开调试的情况下运行！
DEBUG = True

ALLOWED_HOSTS = ['*************', 'localhost', '127.0.0.1']


# Application definition

# INSTALLED_APPS = [
#     'simpleui',#后台美化
#     'django.contrib.admin',
#     'django.contrib.auth',
#     'django.contrib.contenttypes',
#     'django.contrib.sessions',
#     'django.contrib.messages',
#     'django.contrib.staticfiles',
#     'books.apps.BooksConfig',
# ]



INSTALLED_APPS = [
# --- 在这里添加下面这一行 ---
    'corsheaders',
    # 1. 我们自己的应用，放在最前面，以确保它的模板可以覆盖第三方应用
    'books.apps.BooksConfig',

    # 2. 第三方应用
    'simpleui',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.github',
    # 如果以后要加回QQ，就放在这里 'allauth.socialaccount.providers.qq',

    # 3. Django 核心应用
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',

    'rest_framework',
    'rest_framework.authtoken',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    # --- 在这里添加下面这一行 ---
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
   # 为 allauth 添加此行
    "allauth.account.middleware.AccountMiddleware",
]

ROOT_URLCONF = 'Novel_website.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates']
        ,
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                # 'allauth.account.context_processors.account',       # <-- 新增
                # 'allauth.socialaccount.context_processors.socialaccount', # <-- 新增 (如果您使用社交登录)
            ],
        },
    },
]

WSGI_APPLICATION = 'Novel_website.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR / 'db.sqlite3',
#     }
# }
# config/settings.py

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.mysql',
#         'NAME': 'novel_db_reset',           # 数据库名
#         'USER': 'root',               # 数据库用户名
#         'PASSWORD': '123456',         # 数据库密码
#         'HOST': '127.0.0.1',          # 数据库服务器地址，本地就是它
#         'PORT': '3306',               # MySQL 默认端口
#     }
# }
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'novel_db_reset',           # 数据库名
        'USER': 'root',               # 数据库用户名
        'PASSWORD': 'ZS20030206',         # 数据库密码
        'HOST': '*************',          # 数据库服务器地址，本地就是它
        'PORT': '3306',               # MySQL 默认端口
    }
}

# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

# 1. 将语言代码改为简体中文
LANGUAGE_CODE = 'zh-hans'

# 2. 将时区改为中国时区
TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_TZ = False


# 静态文件（CSS、JavaScript、图像）
# https://docs.djangoproject.com/en/5.2/howto/static-files/


# 默认主键字段类型
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


STATIC_ROOT = BASE_DIR / 'staticfiles'

# --- 静态文件配置 ---
# 浏览器访问静态文件时的URL前缀
STATIC_URL = '/static/'
# Django在查找静态文件时，除了会查找每个app下的static目录外，还会查找这个列表里定义的所有目录
STATICFILES_DIRS = [
    BASE_DIR / 'static', # 在项目根目录下创建一个名为 'static' 的文件夹
]

# # --- 媒体文件配置 ---
# # 浏览器访问媒体文件时的URL前缀
# MEDIA_URL = '/media/'
# # 服务器上存储用户上传文件的根目录
# MEDIA_ROOT = BASE_DIR / 'media'


# settings.py

# ----------------- simpleui 自定义配置 -----------------

# 隐藏右上角的 “View Site” 链接
SIMPLEUI_HOME_PAGE = '/'

# 隐藏左侧菜单的 "SimpleUI官网" 和 "反馈"
SIMPLEUI_HOME_INFO = False
SIMPLEUI_ANALYSIS = False

# 设置后台 Logo
SIMPLEUI_LOGO = 'https://cdn.picui.cn/vip/2025/07/11/687114f749898.png'  # 使用 URL
# SIMPLEUI_LOGO = '/static/img/logo.svg'            # 使用本地静态文件

# 设置浏览器标签页的 favicon
# SIMPLEUI_ICON = 'https://example.com/favicon.ico' # 使用 URL
# SIMPLEUI_ICON = '/static/img/favicon.ico'         # 使用本地静态文件

# 默认主题
SIMPLEUI_DEFAULT_THEME = 'e-admin.css' # ElementUI 风格的主题

# 禁用右上角的“主题”切换按钮
SIMPLEUI_SHOW_THEMES = False

# 【这里】设置用户登录成功后，默认跳转的 URL 路径
LOGIN_REDIRECT_URL = '/'  # '/' 表示网站的首页

# 设置用户退出成功后，默认跳转的 URL 路径
LOGOUT_REDIRECT_URL = '/' # '/' 表示网站的首页

LOGIN_URL = 'account_login'

AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
]
SITE_ID = 1

# allauth 相关配置
# 【新增或修改这一行】告诉 allauth 使用我们自定义的账户适配器
# ACCOUNT_ADAPTER = 'books.adapters.MyAccountAdapter'

# 【关键】设置为 False，这样通过QQ登录的新用户不会被自动创建账号
# 相反，他们会被引导至注册页面，页面上会预填写他们QQ的昵称
SOCIALACCOUNT_AUTO_SIGNUP = False

#如果希望完全禁用电子邮件验证功能
ACCOUNT_EMAIL_VERIFICATION = 'none'

# 【新增这一行】允许通过GET请求直接发起社交账号登录
SOCIALACCOUNT_LOGIN_ON_GET = True

# 【新增这一行】告诉 allauth 使用我们自定义的适配器
SOCIALACCOUNT_ADAPTER = 'books.adapters.MySocialAccountAdapter'

# 告诉 Django 使用 SMTP 协议来发送邮件
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
# EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend' # 暂时改为这一行

# SMTP 服务器的设置
EMAIL_HOST = 'smtp.qq.com'          # QQ邮箱的SMTP服务器地址
EMAIL_PORT = 465                    # QQ邮箱推荐的SSL端口号
EMAIL_USE_SSL = True                # 必须开启SSL加密

# 你的邮箱账户和“授权码”
# 【修改】换成你自己的QQ邮箱地址
EMAIL_HOST_USER = '<EMAIL>'
# 【修改】这里填写你刚刚获取到的16位授权码，不是你的QQ登录密码！
EMAIL_HOST_PASSWORD = 'yslpbkzlmutheaic'

# 邮件中显示的发件人地址，通常和上面的邮箱地址一样
DEFAULT_FROM_EMAIL = EMAIL_HOST_USER

# 【新增这一行】调整“用户枚举攻击”的防范策略
# 设置为 False 后，当输入不存在的邮箱时，表单会直接提示错误，而不是发送假邮件
ACCOUNT_PREVENT_ENUMERATION = False

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.TokenAuthentication',  # 将令牌认证设为默认
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticatedOrReadOnly', # 设置默认权限
    ]
}

# --- 在文件末尾添加 CORS 相关配置 ---

# 列出允许访问你的 API 的前端服务器地址
# 5173 是 Vite (新版 Vue/React) 的默认端口, 8080 是 vue-cli 的默认端口
CORS_ALLOWED_ORIGINS = [
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://localhost:8080",
    "http://127.0.0.1:8080",
]

# 在开发阶段，你也可以选择允许所有来源，但这在生产环境中不安全
# CORS_ALLOW_ALL_ORIGINS = True

# 允许前端在请求中携带凭证（如 Cookies 或 Authorization headers）
CORS_ALLOW_CREDENTIALS = True